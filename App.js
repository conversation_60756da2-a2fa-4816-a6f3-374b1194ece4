import React from 'react';
import { NavigationContainer } from '@react-navigation/native';
import { createStackNavigator } from '@react-navigation/stack';
import { StatusBar } from 'expo-status-bar';
import { StyleSheet } from 'react-native';

// Import screens
import HomeScreen from './src/screens/HomeScreen';
import EnglishScreen from './src/screens/EnglishScreen';
import MathScreen from './src/screens/MathScreen';
import HindiScreen from './src/screens/HindiScreen';
import NationalAnthemScreen from './src/screens/NationalAnthemScreen';
import AlphabetScreen from './src/screens/AlphabetScreen';
import RhymesScreen from './src/screens/RhymesScreen';
import NumbersScreen from './src/screens/NumbersScreen';
import HindiAlphabetScreen from './src/screens/HindiAlphabetScreen';

const Stack = createStackNavigator();

export default function App() {
  return (
    <NavigationContainer>
      <StatusBar style="auto" />
      <Stack.Navigator
        initialRouteName="Home"
        screenOptions={{
          headerStyle: {
            backgroundColor: '#4CAF50',
          },
          headerTintColor: '#fff',
          headerTitleStyle: {
            fontWeight: 'bold',
            fontSize: 20,
          },
        }}
      >
        <Stack.Screen 
          name="Home" 
          component={HomeScreen} 
          options={{ title: 'Kids Tutorial App' }}
        />
        <Stack.Screen 
          name="English" 
          component={EnglishScreen} 
          options={{ title: 'English Learning' }}
        />
        <Stack.Screen 
          name="Math" 
          component={MathScreen} 
          options={{ title: 'Math Learning' }}
        />
        <Stack.Screen 
          name="Hindi" 
          component={HindiScreen} 
          options={{ title: 'Hindi Learning' }}
        />
        <Stack.Screen 
          name="NationalAnthem" 
          component={NationalAnthemScreen} 
          options={{ title: 'National Anthem' }}
        />
        <Stack.Screen 
          name="Alphabet" 
          component={AlphabetScreen} 
          options={{ title: 'ABC Learning' }}
        />
        <Stack.Screen 
          name="Rhymes" 
          component={RhymesScreen} 
          options={{ title: 'Nursery Rhymes' }}
        />
        <Stack.Screen 
          name="Numbers" 
          component={NumbersScreen} 
          options={{ title: 'Number Learning' }}
        />
        <Stack.Screen 
          name="HindiAlphabet" 
          component={HindiAlphabetScreen} 
          options={{ title: 'Hindi Alphabet' }}
        />
      </Stack.Navigator>
    </NavigationContainer>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
});
