{"version": 3, "sources": ["../../../../../../../src/start/server/metro/debugging/messageHandlers/VscodeRuntimeEvaluate.ts"], "sourcesContent": ["import type Protocol from 'devtools-protocol';\n\nimport { MessageHandler } from '../MessageHandler';\nimport { getDebuggerType } from '../getDebuggerType';\nimport type { CdpMessage, DebuggerRequest, DeviceResponse } from '../types';\n\n/**\n * Vscode is trying to inject a script to configure Node environment variables.\n * This won't work in <PERSON><PERSON>, but vscode will retry this 200x.\n * Avoid sending this \"spam\" to the device.\n *\n * @see https://github.com/microsoft/vscode-js-debug/blob/1d104b5184736677ab5cc280c70bbd227403850c/src/targets/node/nodeAttacherBase.ts#L22-L54\n */\nexport class VscodeRuntimeEvaluateHandler extends MessageHandler {\n  isEnabled() {\n    return getDebuggerType(this.debugger.userAgent) === 'vscode';\n  }\n\n  handleDebuggerMessage(message: DebuggerRequest<RuntimeEvaluate>) {\n    if (message.method === 'Runtime.evaluate' && isVscodeNodeAttachEnvironmentInjection(message)) {\n      return this.sendToDebugger<DeviceResponse<RuntimeEvaluate>>({\n        id: message.id,\n        result: {\n          result: {\n            type: 'string',\n            value: `Hermes doesn't support environment variables through process.env`,\n          },\n        },\n      });\n    }\n\n    if (message.method === 'Runtime.evaluate' && isVscodeNodeTelemetry(message)) {\n      return this.sendToDebugger<DeviceResponse<RuntimeEvaluate>>({\n        id: message.id,\n        result: {\n          result: {\n            type: 'object',\n            value: {\n              processId: this.page.id,\n              nodeVersion: process.version,\n              architecture: process.arch,\n            },\n          },\n        },\n      });\n    }\n\n    return false;\n  }\n}\n\n/** @see https://chromedevtools.github.io/devtools-protocol/v8/Runtime/#method-evaluate */\nexport type RuntimeEvaluate = CdpMessage<\n  'Runtime.evaluate',\n  Protocol.Runtime.EvaluateRequest,\n  Protocol.Runtime.EvaluateResponse\n>;\n\n/** @see https://github.com/microsoft/vscode-js-debug/blob/1d104b5184736677ab5cc280c70bbd227403850c/src/targets/node/nodeAttacherBase.ts#L22-L54 */\nfunction isVscodeNodeAttachEnvironmentInjection(message: DebuggerRequest<RuntimeEvaluate>) {\n  return (\n    message.params?.expression.includes(`typeof process==='undefined'`) &&\n    message.params?.expression.includes(`'process not defined'`) &&\n    message.params?.expression.includes(`process.env[\"NODE_OPTIONS\"]`)\n  );\n}\n\n/** @see https://github.com/microsoft/vscode-js-debug/blob/1d104b5184736677ab5cc280c70bbd227403850c/src/targets/node/nodeLauncherBase.ts#L523-L531 */\nfunction isVscodeNodeTelemetry(message: DebuggerRequest<RuntimeEvaluate>) {\n  return (\n    message.params?.expression.includes(`typeof process === 'undefined'`) &&\n    message.params?.expression.includes(`'process not defined'`) &&\n    message.params?.expression.includes(`process.pid`) &&\n    message.params?.expression.includes(`process.version`) &&\n    message.params?.expression.includes(`process.arch`)\n  );\n}\n"], "names": ["VscodeRuntimeEvaluateHandler", "MessageHandler", "isEnabled", "getDebuggerType", "debugger", "userAgent", "handleDebuggerMessage", "message", "method", "isVscodeNodeAttachEnvironmentInjection", "sendToDebugger", "id", "result", "type", "value", "isVscodeNodeTelemetry", "processId", "page", "nodeVersion", "process", "version", "architecture", "arch", "params", "expression", "includes"], "mappings": ";;;;+BAaaA;;;eAAAA;;;gCAXkB;iCACC;AAUzB,MAAMA,qCAAqCC,8BAAc;IAC9DC,YAAY;QACV,OAAOC,IAAAA,gCAAe,EAAC,IAAI,CAACC,QAAQ,CAACC,SAAS,MAAM;IACtD;IAEAC,sBAAsBC,OAAyC,EAAE;QAC/D,IAAIA,QAAQC,MAAM,KAAK,sBAAsBC,uCAAuCF,UAAU;YAC5F,OAAO,IAAI,CAACG,cAAc,CAAkC;gBAC1DC,IAAIJ,QAAQI,EAAE;gBACdC,QAAQ;oBACNA,QAAQ;wBACNC,MAAM;wBACNC,OAAO,CAAC,gEAAgE,CAAC;oBAC3E;gBACF;YACF;QACF;QAEA,IAAIP,QAAQC,MAAM,KAAK,sBAAsBO,sBAAsBR,UAAU;YAC3E,OAAO,IAAI,CAACG,cAAc,CAAkC;gBAC1DC,IAAIJ,QAAQI,EAAE;gBACdC,QAAQ;oBACNA,QAAQ;wBACNC,MAAM;wBACNC,OAAO;4BACLE,WAAW,IAAI,CAACC,IAAI,CAACN,EAAE;4BACvBO,aAAaC,QAAQC,OAAO;4BAC5BC,cAAcF,QAAQG,IAAI;wBAC5B;oBACF;gBACF;YACF;QACF;QAEA,OAAO;IACT;AACF;AASA,iJAAiJ,GACjJ,SAASb,uCAAuCF,OAAyC;QAErFA,iBACAA,kBACAA;IAHF,OACEA,EAAAA,kBAAAA,QAAQgB,MAAM,qBAAdhB,gBAAgBiB,UAAU,CAACC,QAAQ,CAAC,CAAC,4BAA4B,CAAC,QAClElB,mBAAAA,QAAQgB,MAAM,qBAAdhB,iBAAgBiB,UAAU,CAACC,QAAQ,CAAC,CAAC,qBAAqB,CAAC,QAC3DlB,mBAAAA,QAAQgB,MAAM,qBAAdhB,iBAAgBiB,UAAU,CAACC,QAAQ,CAAC,CAAC,2BAA2B,CAAC;AAErE;AAEA,mJAAmJ,GACnJ,SAASV,sBAAsBR,OAAyC;QAEpEA,iBACAA,kBACAA,kBACAA,kBACAA;IALF,OACEA,EAAAA,kBAAAA,QAAQgB,MAAM,qBAAdhB,gBAAgBiB,UAAU,CAACC,QAAQ,CAAC,CAAC,8BAA8B,CAAC,QACpElB,mBAAAA,QAAQgB,MAAM,qBAAdhB,iBAAgBiB,UAAU,CAACC,QAAQ,CAAC,CAAC,qBAAqB,CAAC,QAC3DlB,mBAAAA,QAAQgB,MAAM,qBAAdhB,iBAAgBiB,UAAU,CAACC,QAAQ,CAAC,CAAC,WAAW,CAAC,QACjDlB,mBAAAA,QAAQgB,MAAM,qBAAdhB,iBAAgBiB,UAAU,CAACC,QAAQ,CAAC,CAAC,eAAe,CAAC,QACrDlB,mBAAAA,QAAQgB,MAAM,qBAAdhB,iBAAgBiB,UAAU,CAACC,QAAQ,CAAC,CAAC,YAAY,CAAC;AAEtD"}