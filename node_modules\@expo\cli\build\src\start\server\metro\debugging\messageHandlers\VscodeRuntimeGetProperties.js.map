{"version": 3, "sources": ["../../../../../../../src/start/server/metro/debugging/messageHandlers/VscodeRuntimeGetProperties.ts"], "sourcesContent": ["import type Protocol from 'devtools-protocol';\n\nimport { <PERSON><PERSON>and<PERSON> } from '../MessageHandler';\nimport { getDebuggerType } from '../getDebuggerType';\nimport type { CdpMessage, DebuggerRequest, DeviceResponse } from '../types';\n\n/**\n * Vscode doesn't seem to work nicely with missing `description` fields on `RemoteObject` instances.\n * It also tries to invoke `Runtime.callFunctionOn` on `Symbol` types, which crashes Hermes.\n * This handler tries to compensate for these two separate issues.\n *\n * @see https://github.com/facebook/hermes/issues/114\n * @see https://github.com/microsoft/vscode-js-debug/issues/1583\n */\nexport class VscodeRuntimeGetPropertiesHandler extends MessageHandler {\n  /** Keep track of `Runtime.getProperties` responses to intercept, by request id */\n  interceptGetProperties = new Set<number>();\n\n  isEnabled() {\n    return getDebuggerType(this.debugger.userAgent) === 'vscode';\n  }\n\n  handleDebuggerMessage(message: DebuggerRequest<RuntimeGetProperties>) {\n    if (message.method === 'Runtime.getProperties') {\n      this.interceptGetProperties.add(message.id);\n    }\n\n    // Do not block propagation of this message\n    return false;\n  }\n\n  handleDeviceMessage(message: DeviceResponse<RuntimeGetProperties>) {\n    if ('id' in message && this.interceptGetProperties.has(message.id)) {\n      this.interceptGetProperties.delete(message.id);\n\n      for (const item of message.result.result ?? []) {\n        // Force-fully format the properties description to be an empty string\n        if (item.value) {\n          item.value.description = item.value.description ?? '';\n        }\n\n        // Avoid passing the `objectId` for symbol types.\n        // When collapsing in vscode, it will fetch information about the symbol using the `objectId`.\n        // The `Runtime.getProperties` request of the symbol hard-crashes Hermes.\n        if (item.value?.type === 'symbol' && item.value.objectId) {\n          delete item.value.objectId;\n        }\n      }\n    }\n\n    // Do not block propagation of this message\n    return false;\n  }\n}\n\n/** @see https://chromedevtools.github.io/devtools-protocol/v8/Runtime/#method-getProperties */\nexport type RuntimeGetProperties = CdpMessage<\n  'Runtime.getProperties',\n  Protocol.Runtime.GetPropertiesRequest,\n  Protocol.Runtime.GetPropertiesResponse\n>;\n"], "names": ["VscodeRuntimeGetPropertiesHandler", "MessageHandler", "isEnabled", "getDebuggerType", "debugger", "userAgent", "handleDebuggerMessage", "message", "method", "interceptGetProperties", "add", "id", "handleDeviceMessage", "has", "delete", "item", "result", "value", "description", "type", "objectId", "Set"], "mappings": ";;;;+BAcaA;;;eAAAA;;;gCAZkB;iCACC;AAWzB,MAAMA,0CAA0CC,8BAAc;IAInEC,YAAY;QACV,OAAOC,IAAAA,gCAAe,EAAC,IAAI,CAACC,QAAQ,CAACC,SAAS,MAAM;IACtD;IAEAC,sBAAsBC,OAA8C,EAAE;QACpE,IAAIA,QAAQC,MAAM,KAAK,yBAAyB;YAC9C,IAAI,CAACC,sBAAsB,CAACC,GAAG,CAACH,QAAQI,EAAE;QAC5C;QAEA,2CAA2C;QAC3C,OAAO;IACT;IAEAC,oBAAoBL,OAA6C,EAAE;QACjE,IAAI,QAAQA,WAAW,IAAI,CAACE,sBAAsB,CAACI,GAAG,CAACN,QAAQI,EAAE,GAAG;YAClE,IAAI,CAACF,sBAAsB,CAACK,MAAM,CAACP,QAAQI,EAAE;YAE7C,KAAK,MAAMI,QAAQR,QAAQS,MAAM,CAACA,MAAM,IAAI,EAAE,CAAE;oBAS1CD;gBARJ,sEAAsE;gBACtE,IAAIA,KAAKE,KAAK,EAAE;oBACdF,KAAKE,KAAK,CAACC,WAAW,GAAGH,KAAKE,KAAK,CAACC,WAAW,IAAI;gBACrD;gBAEA,iDAAiD;gBACjD,8FAA8F;gBAC9F,yEAAyE;gBACzE,IAAIH,EAAAA,cAAAA,KAAKE,KAAK,qBAAVF,YAAYI,IAAI,MAAK,YAAYJ,KAAKE,KAAK,CAACG,QAAQ,EAAE;oBACxD,OAAOL,KAAKE,KAAK,CAACG,QAAQ;gBAC5B;YACF;QACF;QAEA,2CAA2C;QAC3C,OAAO;IACT;;QAtCK,gBACL,gFAAgF,QAChFX,yBAAyB,IAAIY;;AAqC/B"}