{"version": 3, "sources": ["../../../../src/start/server/serverLogLikeMetro.ts"], "sourcesContent": ["/**\n * Copyright © 2024 650 Industries.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\nimport { INTERNAL_CALLSITES_REGEX } from '@expo/metro-config';\nimport chalk from 'chalk';\nimport path from 'path';\n// @ts-expect-error\nimport { mapSourcePosition } from 'source-map-support';\nimport * as stackTraceParser from 'stacktrace-parser';\n\nimport { parseErrorStack, StackFrame } from './metro/log-box/LogBoxSymbolication';\nimport { env } from '../../utils/env';\nimport { memoize } from '../../utils/fn';\nimport { LogBoxLog } from './metro/log-box/LogBoxLog';\nimport { getStackAsFormattedLog } from './metro/metroErrorInterface';\n\nconst debug = require('debug')('expo:metro:logger') as typeof console.log;\n\nconst groupStack: any = [];\nlet collapsedGuardTimer: ReturnType<typeof setTimeout> | undefined;\n\nexport function logLikeMetro(\n  originalLogFunction: (...args: any[]) => void,\n  level: string,\n  platform: string,\n  ...data: any[]\n) {\n  // @ts-expect-error\n  const logFunction = console[level] && level !== 'trace' ? level : 'log';\n  const color =\n    level === 'error'\n      ? chalk.inverse.red\n      : level === 'warn'\n        ? chalk.inverse.yellow\n        : chalk.inverse.white;\n\n  if (level === 'group') {\n    groupStack.push(level);\n  } else if (level === 'groupCollapsed') {\n    groupStack.push(level);\n    clearTimeout(collapsedGuardTimer);\n    // Inform users that logs get swallowed if they forget to call `groupEnd`.\n    collapsedGuardTimer = setTimeout(() => {\n      if (groupStack.includes('groupCollapsed')) {\n        originalLogFunction(\n          chalk.inverse.yellow.bold(' WARN '),\n          'Expected `console.groupEnd` to be called after `console.groupCollapsed`.'\n        );\n        groupStack.length = 0;\n      }\n    }, 3000);\n    return;\n  } else if (level === 'groupEnd') {\n    groupStack.pop();\n    if (!groupStack.length) {\n      clearTimeout(collapsedGuardTimer);\n    }\n    return;\n  }\n\n  if (!groupStack.includes('groupCollapsed')) {\n    // Remove excess whitespace at the end of a log message, if possible.\n    const lastItem = data[data.length - 1];\n    if (typeof lastItem === 'string') {\n      data[data.length - 1] = lastItem.trimEnd();\n    }\n\n    const modePrefix = platform === '' ? '' : chalk.bold`${platform} `;\n    originalLogFunction(\n      modePrefix +\n        color.bold(` ${logFunction.toUpperCase()} `) +\n        ''.padEnd(groupStack.length * 2, ' '),\n      ...data\n    );\n  }\n}\n\nconst escapedPathSep = path.sep === '\\\\' ? '\\\\\\\\' : path.sep;\nconst SERVER_STACK_MATCHER = new RegExp(\n  `${escapedPathSep}(react-dom|metro-runtime|expo-router)${escapedPathSep}`\n);\n\nexport async function maybeSymbolicateAndFormatReactErrorLogAsync(\n  projectRoot: string,\n  level: 'error' | 'warn',\n  error: {\n    message: string;\n    stack: StackFrame[];\n  }\n): Promise<string> {\n  const log = new LogBoxLog({\n    level: level as 'error' | 'warn',\n    message: {\n      content: error.message,\n      substitutions: [],\n    },\n    isComponentError: false,\n    stack: error.stack,\n    category: 'static',\n    componentStack: [],\n  });\n\n  await new Promise((res) => log.symbolicate('stack', res));\n\n  const symbolicatedErrorMessageAndStackLog = [\n    log.message.content,\n    getStackAsFormattedLog(projectRoot, {\n      stack: log.symbolicated?.stack?.stack ?? [],\n      codeFrame: log.codeFrame,\n    }),\n  ].join('\\n\\n');\n\n  return symbolicatedErrorMessageAndStackLog;\n}\n\n/** Attempt to parse an error message string to an unsymbolicated stack.  */\nexport function parseErrorStringToObject(errorString: string) {\n  // Find the first line of the possible stack trace\n  const stackStartIndex = errorString.indexOf('\\n    at ');\n  if (stackStartIndex === -1) {\n    // No stack trace found, return the original error string\n    return null;\n  }\n  const message = errorString.slice(0, stackStartIndex).trim();\n  const stack = errorString.slice(stackStartIndex + 1);\n\n  try {\n    const parsedStack = parseErrorStack(stack);\n\n    return {\n      message,\n      stack: parsedStack,\n    };\n  } catch (e) {\n    // If parsing fails, return the original error string\n    debug('Failed to parse error stack:', e);\n    return null;\n  }\n}\n\nfunction augmentLogsInternal(projectRoot: string) {\n  const augmentLog = (name: string, fn: typeof console.log) => {\n    // @ts-expect-error: TypeScript doesn't know about polyfilled functions.\n    if (fn.__polyfilled) {\n      return fn;\n    }\n    const originalFn = fn.bind(console);\n    function logWithStack(...args: any[]) {\n      const stack = new Error().stack;\n      // Check if the log originates from the server.\n      const isServerLog = !!stack?.match(SERVER_STACK_MATCHER);\n\n      if (isServerLog) {\n        if (name === 'error' || name === 'warn') {\n          if (\n            args.length === 2 &&\n            typeof args[1] === 'string' &&\n            args[1].trim().startsWith('at ')\n          ) {\n            // react-dom custom stacks which are always broken.\n            // A stack string like:\n            //    at div\n            //    at http://localhost:8081/node_modules/expo-router/node/render.bundle?platform=web&dev=true&hot=false&transform.engine=hermes&transform.routerRoot=app&resolver.environment=node&transform.environment=node:38008:27\n            //    at Background (http://localhost:8081/node_modules/expo-router/node/render.bundle?platform=web&dev=true&hot=false&transform.engine=hermes&transform.routerRoot=app&resolver.environment=node&transform.environment=node:151009:7)\n            const customStack = args[1];\n\n            try {\n              const parsedStack = parseErrorStack(customStack);\n              const symbolicatedStack = parsedStack.map((line: any) => {\n                const mapped = mapSourcePosition({\n                  source: line.file,\n                  line: line.lineNumber,\n                  column: line.column,\n                }) as {\n                  // '/Users/<USER>/Documents/GitHub/lab/sdk51-beta/node_modules/react-native-web/dist/exports/View/index.js',\n                  source: string;\n                  line: number;\n                  column: number;\n                  // 'hrefAttrs'\n                  name: string | null;\n                };\n\n                const fallbackName = mapped.name ?? '<unknown>';\n                return {\n                  file: mapped.source,\n                  lineNumber: mapped.line,\n                  column: mapped.column,\n                  // Attempt to preserve the react component name if possible.\n                  methodName: line.methodName\n                    ? line.methodName === '<unknown>'\n                      ? fallbackName\n                      : line.methodName\n                    : fallbackName,\n                  arguments: line.arguments ?? [],\n                };\n              });\n\n              // Replace args[1] with the formatted stack.\n              args[1] = '\\n' + formatParsedStackLikeMetro(projectRoot, symbolicatedStack, true);\n            } catch {\n              // If symbolication fails, log the original stack.\n              args.push('\\n' + formatStackLikeMetro(projectRoot, customStack));\n            }\n          } else {\n            args.push('\\n' + formatStackLikeMetro(projectRoot, stack!));\n          }\n        }\n\n        logLikeMetro(originalFn, name, 'λ', ...args);\n      } else {\n        originalFn(...args);\n      }\n    }\n    logWithStack.__polyfilled = true;\n    return logWithStack;\n  };\n\n  ['trace', 'info', 'error', 'warn', 'log', 'group', 'groupCollapsed', 'groupEnd', 'debug'].forEach(\n    (name) => {\n      // @ts-expect-error\n      console[name] = augmentLog(name, console[name]);\n    }\n  );\n}\n\nexport function formatStackLikeMetro(projectRoot: string, stack: string) {\n  // Remove `Error: ` from the beginning of the stack trace.\n  // Dim traces that match `INTERNAL_CALLSITES_REGEX`\n\n  const stackTrace = stackTraceParser.parse(stack);\n  return formatParsedStackLikeMetro(projectRoot, stackTrace);\n}\n\nfunction formatParsedStackLikeMetro(\n  projectRoot: string,\n  stackTrace: stackTraceParser.StackFrame[],\n  isComponentStack = false\n) {\n  // Remove `Error: ` from the beginning of the stack trace.\n  // Dim traces that match `INTERNAL_CALLSITES_REGEX`\n\n  return stackTrace\n    .filter(\n      (line) =>\n        line.file &&\n        // Ignore unsymbolicated stack frames. It's not clear how this is possible but it sometimes happens when the graph changes.\n        !/^https?:\\/\\//.test(line.file) &&\n        (isComponentStack ? true : line.file !== '<anonymous>')\n    )\n    .map((line) => {\n      // Use the same regex we use in Metro config to filter out traces:\n      const isCollapsed = INTERNAL_CALLSITES_REGEX.test(line.file!);\n      if (!isComponentStack && isCollapsed && !env.EXPO_DEBUG) {\n        return null;\n      }\n      // If a file is collapsed, print it with dim styling.\n      const style = isCollapsed ? chalk.dim : chalk.gray;\n      // Use the `at` prefix to match Node.js\n      let fileName = line.file!;\n      if (fileName.startsWith(path.sep)) {\n        fileName = path.relative(projectRoot, fileName);\n      }\n      if (line.lineNumber != null) {\n        fileName += `:${line.lineNumber}`;\n        if (line.column != null) {\n          fileName += `:${line.column}`;\n        }\n      }\n\n      return style(`  ${line.methodName} (${fileName})`);\n    })\n    .filter(Boolean)\n    .join('\\n');\n}\n\n/** Augment console logs to check the stack trace and format like Metro logs if we think the log came from the SSR renderer or an API route. */\nexport const augmentLogs = memoize(augmentLogsInternal);\n"], "names": ["augmentLogs", "formatStackLikeMetro", "logLikeMetro", "maybeSymbolicateAndFormatReactErrorLogAsync", "parseErrorStringToObject", "debug", "require", "groupStack", "<PERSON><PERSON><PERSON><PERSON><PERSON>r", "originalLogFunction", "level", "platform", "data", "logFunction", "console", "color", "chalk", "inverse", "red", "yellow", "white", "push", "clearTimeout", "setTimeout", "includes", "bold", "length", "pop", "lastItem", "trimEnd", "modePrefix", "toUpperCase", "padEnd", "escapedPathSep", "path", "sep", "SERVER_STACK_MATCHER", "RegExp", "projectRoot", "error", "log", "LogBoxLog", "message", "content", "substitutions", "isComponentError", "stack", "category", "componentStack", "Promise", "res", "symbolicate", "symbolicatedErrorMessageAndStackLog", "getStackAsFormattedLog", "symbolicated", "codeFrame", "join", "errorString", "stackStartIndex", "indexOf", "slice", "trim", "parsedStack", "parseError<PERSON>tack", "e", "augmentLogsInternal", "augmentLog", "name", "fn", "__polyfilled", "originalFn", "bind", "logWithStack", "args", "Error", "isServerLog", "match", "startsWith", "customStack", "symbolicatedStack", "map", "line", "mapped", "mapSourcePosition", "source", "file", "lineNumber", "column", "fallback<PERSON><PERSON>", "methodName", "arguments", "formatParsedStackLikeMetro", "for<PERSON>ach", "stackTrace", "stackTrace<PERSON><PERSON><PERSON>", "parse", "isComponentStack", "filter", "test", "isCollapsed", "INTERNAL_CALLSITES_REGEX", "env", "EXPO_DEBUG", "style", "dim", "gray", "fileName", "relative", "Boolean", "memoize"], "mappings": "AAAA;;;;;CAKC;;;;;;;;;;;IAkRYA,WAAW;eAAXA;;IAnDGC,oBAAoB;eAApBA;;IA5MAC,YAAY;eAAZA;;IA6DMC,2CAA2C;eAA3CA;;IAkCNC,wBAAwB;eAAxBA;;;;yBAjHyB;;;;;;;gEACvB;;;;;;;gEACD;;;;;;;yBAEiB;;;;;;;iEACA;;;;;;qCAEU;qBACxB;oBACI;2BACE;qCACa;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEvC,MAAMC,QAAQC,QAAQ,SAAS;AAE/B,MAAMC,aAAkB,EAAE;AAC1B,IAAIC;AAEG,SAASN,aACdO,mBAA6C,EAC7CC,KAAa,EACbC,QAAgB,EAChB,GAAGC,IAAW;IAEd,mBAAmB;IACnB,MAAMC,cAAcC,OAAO,CAACJ,MAAM,IAAIA,UAAU,UAAUA,QAAQ;IAClE,MAAMK,QACJL,UAAU,UACNM,gBAAK,CAACC,OAAO,CAACC,GAAG,GACjBR,UAAU,SACRM,gBAAK,CAACC,OAAO,CAACE,MAAM,GACpBH,gBAAK,CAACC,OAAO,CAACG,KAAK;IAE3B,IAAIV,UAAU,SAAS;QACrBH,WAAWc,IAAI,CAACX;IAClB,OAAO,IAAIA,UAAU,kBAAkB;QACrCH,WAAWc,IAAI,CAACX;QAChBY,aAAad;QACb,0EAA0E;QAC1EA,sBAAsBe,WAAW;YAC/B,IAAIhB,WAAWiB,QAAQ,CAAC,mBAAmB;gBACzCf,oBACEO,gBAAK,CAACC,OAAO,CAACE,MAAM,CAACM,IAAI,CAAC,WAC1B;gBAEFlB,WAAWmB,MAAM,GAAG;YACtB;QACF,GAAG;QACH;IACF,OAAO,IAAIhB,UAAU,YAAY;QAC/BH,WAAWoB,GAAG;QACd,IAAI,CAACpB,WAAWmB,MAAM,EAAE;YACtBJ,aAAad;QACf;QACA;IACF;IAEA,IAAI,CAACD,WAAWiB,QAAQ,CAAC,mBAAmB;QAC1C,qEAAqE;QACrE,MAAMI,WAAWhB,IAAI,CAACA,KAAKc,MAAM,GAAG,EAAE;QACtC,IAAI,OAAOE,aAAa,UAAU;YAChChB,IAAI,CAACA,KAAKc,MAAM,GAAG,EAAE,GAAGE,SAASC,OAAO;QAC1C;QAEA,MAAMC,aAAanB,aAAa,KAAK,KAAKK,gBAAK,CAACS,IAAI,CAAC,EAAEd,SAAS,CAAC,CAAC;QAClEF,oBACEqB,aACEf,MAAMU,IAAI,CAAC,CAAC,CAAC,EAAEZ,YAAYkB,WAAW,GAAG,CAAC,CAAC,IAC3C,GAAGC,MAAM,CAACzB,WAAWmB,MAAM,GAAG,GAAG,SAChCd;IAEP;AACF;AAEA,MAAMqB,iBAAiBC,eAAI,CAACC,GAAG,KAAK,OAAO,SAASD,eAAI,CAACC,GAAG;AAC5D,MAAMC,uBAAuB,IAAIC,OAC/B,GAAGJ,eAAe,qCAAqC,EAAEA,gBAAgB;AAGpE,eAAe9B,4CACpBmC,WAAmB,EACnB5B,KAAuB,EACvB6B,KAGC;QAmBUC,yBAAAA;IAjBX,MAAMA,MAAM,IAAIC,oBAAS,CAAC;QACxB/B,OAAOA;QACPgC,SAAS;YACPC,SAASJ,MAAMG,OAAO;YACtBE,eAAe,EAAE;QACnB;QACAC,kBAAkB;QAClBC,OAAOP,MAAMO,KAAK;QAClBC,UAAU;QACVC,gBAAgB,EAAE;IACpB;IAEA,MAAM,IAAIC,QAAQ,CAACC,MAAQV,IAAIW,WAAW,CAAC,SAASD;IAEpD,MAAME,sCAAsC;QAC1CZ,IAAIE,OAAO,CAACC,OAAO;QACnBU,IAAAA,2CAAsB,EAACf,aAAa;YAClCQ,OAAON,EAAAA,oBAAAA,IAAIc,YAAY,sBAAhBd,0BAAAA,kBAAkBM,KAAK,qBAAvBN,wBAAyBM,KAAK,KAAI,EAAE;YAC3CS,WAAWf,IAAIe,SAAS;QAC1B;KACD,CAACC,IAAI,CAAC;IAEP,OAAOJ;AACT;AAGO,SAAShD,yBAAyBqD,WAAmB;IAC1D,kDAAkD;IAClD,MAAMC,kBAAkBD,YAAYE,OAAO,CAAC;IAC5C,IAAID,oBAAoB,CAAC,GAAG;QAC1B,yDAAyD;QACzD,OAAO;IACT;IACA,MAAMhB,UAAUe,YAAYG,KAAK,CAAC,GAAGF,iBAAiBG,IAAI;IAC1D,MAAMf,QAAQW,YAAYG,KAAK,CAACF,kBAAkB;IAElD,IAAI;QACF,MAAMI,cAAcC,IAAAA,oCAAe,EAACjB;QAEpC,OAAO;YACLJ;YACAI,OAAOgB;QACT;IACF,EAAE,OAAOE,GAAG;QACV,qDAAqD;QACrD3D,MAAM,gCAAgC2D;QACtC,OAAO;IACT;AACF;AAEA,SAASC,oBAAoB3B,WAAmB;IAC9C,MAAM4B,aAAa,CAACC,MAAcC;QAChC,wEAAwE;QACxE,IAAIA,GAAGC,YAAY,EAAE;YACnB,OAAOD;QACT;QACA,MAAME,aAAaF,GAAGG,IAAI,CAACzD;QAC3B,SAAS0D,aAAa,GAAGC,IAAW;YAClC,MAAM3B,QAAQ,IAAI4B,QAAQ5B,KAAK;YAC/B,+CAA+C;YAC/C,MAAM6B,cAAc,CAAC,EAAC7B,yBAAAA,MAAO8B,KAAK,CAACxC;YAEnC,IAAIuC,aAAa;gBACf,IAAIR,SAAS,WAAWA,SAAS,QAAQ;oBACvC,IACEM,KAAK/C,MAAM,KAAK,KAChB,OAAO+C,IAAI,CAAC,EAAE,KAAK,YACnBA,IAAI,CAAC,EAAE,CAACZ,IAAI,GAAGgB,UAAU,CAAC,QAC1B;wBACA,mDAAmD;wBACnD,uBAAuB;wBACvB,YAAY;wBACZ,yNAAyN;wBACzN,sOAAsO;wBACtO,MAAMC,cAAcL,IAAI,CAAC,EAAE;wBAE3B,IAAI;4BACF,MAAMX,cAAcC,IAAAA,oCAAe,EAACe;4BACpC,MAAMC,oBAAoBjB,YAAYkB,GAAG,CAAC,CAACC;gCACzC,MAAMC,SAASC,IAAAA,qCAAiB,EAAC;oCAC/BC,QAAQH,KAAKI,IAAI;oCACjBJ,MAAMA,KAAKK,UAAU;oCACrBC,QAAQN,KAAKM,MAAM;gCACrB;gCASA,MAAMC,eAAeN,OAAOf,IAAI,IAAI;gCACpC,OAAO;oCACLkB,MAAMH,OAAOE,MAAM;oCACnBE,YAAYJ,OAAOD,IAAI;oCACvBM,QAAQL,OAAOK,MAAM;oCACrB,4DAA4D;oCAC5DE,YAAYR,KAAKQ,UAAU,GACvBR,KAAKQ,UAAU,KAAK,cAClBD,eACAP,KAAKQ,UAAU,GACjBD;oCACJE,WAAWT,KAAKS,SAAS,IAAI,EAAE;gCACjC;4BACF;4BAEA,4CAA4C;4BAC5CjB,IAAI,CAAC,EAAE,GAAG,OAAOkB,2BAA2BrD,aAAayC,mBAAmB;wBAC9E,EAAE,OAAM;4BACN,kDAAkD;4BAClDN,KAAKpD,IAAI,CAAC,OAAOpB,qBAAqBqC,aAAawC;wBACrD;oBACF,OAAO;wBACLL,KAAKpD,IAAI,CAAC,OAAOpB,qBAAqBqC,aAAaQ;oBACrD;gBACF;gBAEA5C,aAAaoE,YAAYH,MAAM,QAAQM;YACzC,OAAO;gBACLH,cAAcG;YAChB;QACF;QACAD,aAAaH,YAAY,GAAG;QAC5B,OAAOG;IACT;IAEA;QAAC;QAAS;QAAQ;QAAS;QAAQ;QAAO;QAAS;QAAkB;QAAY;KAAQ,CAACoB,OAAO,CAC/F,CAACzB;QACC,mBAAmB;QACnBrD,OAAO,CAACqD,KAAK,GAAGD,WAAWC,MAAMrD,OAAO,CAACqD,KAAK;IAChD;AAEJ;AAEO,SAASlE,qBAAqBqC,WAAmB,EAAEQ,KAAa;IACrE,0DAA0D;IAC1D,mDAAmD;IAEnD,MAAM+C,aAAaC,oBAAiBC,KAAK,CAACjD;IAC1C,OAAO6C,2BAA2BrD,aAAauD;AACjD;AAEA,SAASF,2BACPrD,WAAmB,EACnBuD,UAAyC,EACzCG,mBAAmB,KAAK;IAExB,0DAA0D;IAC1D,mDAAmD;IAEnD,OAAOH,WACJI,MAAM,CACL,CAAChB,OACCA,KAAKI,IAAI,IACT,2HAA2H;QAC3H,CAAC,eAAea,IAAI,CAACjB,KAAKI,IAAI,KAC7BW,CAAAA,mBAAmB,OAAOf,KAAKI,IAAI,KAAK,aAAY,GAExDL,GAAG,CAAC,CAACC;QACJ,kEAAkE;QAClE,MAAMkB,cAAcC,uCAAwB,CAACF,IAAI,CAACjB,KAAKI,IAAI;QAC3D,IAAI,CAACW,oBAAoBG,eAAe,CAACE,QAAG,CAACC,UAAU,EAAE;YACvD,OAAO;QACT;QACA,qDAAqD;QACrD,MAAMC,QAAQJ,cAAcnF,gBAAK,CAACwF,GAAG,GAAGxF,gBAAK,CAACyF,IAAI;QAClD,uCAAuC;QACvC,IAAIC,WAAWzB,KAAKI,IAAI;QACxB,IAAIqB,SAAS7B,UAAU,CAAC3C,eAAI,CAACC,GAAG,GAAG;YACjCuE,WAAWxE,eAAI,CAACyE,QAAQ,CAACrE,aAAaoE;QACxC;QACA,IAAIzB,KAAKK,UAAU,IAAI,MAAM;YAC3BoB,YAAY,CAAC,CAAC,EAAEzB,KAAKK,UAAU,EAAE;YACjC,IAAIL,KAAKM,MAAM,IAAI,MAAM;gBACvBmB,YAAY,CAAC,CAAC,EAAEzB,KAAKM,MAAM,EAAE;YAC/B;QACF;QAEA,OAAOgB,MAAM,CAAC,EAAE,EAAEtB,KAAKQ,UAAU,CAAC,EAAE,EAAEiB,SAAS,CAAC,CAAC;IACnD,GACCT,MAAM,CAACW,SACPpD,IAAI,CAAC;AACV;AAGO,MAAMxD,cAAc6G,IAAAA,WAAO,EAAC5C"}