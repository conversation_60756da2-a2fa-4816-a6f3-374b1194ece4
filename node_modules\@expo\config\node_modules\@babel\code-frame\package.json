{"name": "@babel/code-frame", "version": "7.10.4", "description": "Generate errors that contain a code frame that point to source locations.", "author": "<PERSON> <<EMAIL>>", "homepage": "https://babeljs.io/", "license": "MIT", "publishConfig": {"access": "public"}, "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-code-frame"}, "main": "lib/index.js", "dependencies": {"@babel/highlight": "^7.10.4"}, "devDependencies": {"chalk": "^2.0.0", "strip-ansi": "^4.0.0"}, "gitHead": "7fd40d86a0d03ff0e9c3ea16b29689945433d4df"}