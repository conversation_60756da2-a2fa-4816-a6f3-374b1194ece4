{"name": "kids-tutorial-app", "version": "1.0.0", "main": "node_modules/expo/AppEntry.js", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web"}, "dependencies": {"@react-navigation/bottom-tabs": "^6.5.11", "@react-navigation/native": "^6.1.9", "@react-navigation/stack": "^6.3.20", "expo": "^53.0.19", "expo-av": "~13.4.1", "expo-font": "~11.4.0", "expo-status-bar": "~1.6.0", "react": "18.2.0", "react-native": "^0.72.17", "react-native-gesture-handler": "~2.12.0", "react-native-reanimated": "~3.3.0", "react-native-safe-area-context": "4.6.3", "react-native-screens": "~3.22.0", "react-native-vector-icons": "^10.0.2"}, "devDependencies": {"@babel/core": "^7.20.0"}, "private": true}