import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  Dimensions,
  Alert,
} from 'react-native';

const { width } = Dimensions.get('window');

const AlphabetScreen = () => {
  const [selectedLetter, setSelectedLetter] = useState(null);

  const alphabetData = [
    { letter: 'A', word: 'Apple', emoji: '🍎' },
    { letter: 'B', word: 'Ball', emoji: '⚽' },
    { letter: 'C', word: 'Cat', emoji: '🐱' },
    { letter: 'D', word: 'Dog', emoji: '🐶' },
    { letter: 'E', word: 'Elephant', emoji: '🐘' },
    { letter: 'F', word: 'Fish', emoji: '🐟' },
    { letter: 'G', word: 'Grapes', emoji: '🍇' },
    { letter: 'H', word: 'House', emoji: '🏠' },
    { letter: 'I', word: 'Ice cream', emoji: '🍦' },
    { letter: 'J', word: 'Juice', emoji: '🧃' },
    { letter: 'K', word: 'Kite', emoji: '🪁' },
    { letter: 'L', word: 'Lion', emoji: '🦁' },
    { letter: 'M', word: 'Monkey', emoji: '🐵' },
    { letter: 'N', word: 'Nest', emoji: '🪺' },
    { letter: 'O', word: 'Orange', emoji: '🍊' },
    { letter: 'P', word: 'Parrot', emoji: '🦜' },
    { letter: 'Q', word: 'Queen', emoji: '👸' },
    { letter: 'R', word: 'Rabbit', emoji: '🐰' },
    { letter: 'S', word: 'Sun', emoji: '☀️' },
    { letter: 'T', word: 'Tiger', emoji: '🐅' },
    { letter: 'U', word: 'Umbrella', emoji: '☂️' },
    { letter: 'V', word: 'Van', emoji: '🚐' },
    { letter: 'W', word: 'Watch', emoji: '⌚' },
    { letter: 'X', word: 'Xylophone', emoji: '🎵' },
    { letter: 'Y', word: 'Yak', emoji: '🐂' },
    { letter: 'Z', word: 'Zebra', emoji: '🦓' },
  ];

  const handleLetterPress = (item) => {
    setSelectedLetter(item);
    // Here you could add sound pronunciation
    Alert.alert(
      `Letter ${item.letter}`,
      `${item.letter} for ${item.word} ${item.emoji}`,
      [{ text: 'OK', onPress: () => setSelectedLetter(null) }]
    );
  };

  const renderAlphabetItem = (item, index) => (
    <TouchableOpacity
      key={index}
      style={[
        styles.alphabetCard,
        selectedLetter?.letter === item.letter && styles.selectedCard
      ]}
      onPress={() => handleLetterPress(item)}
      activeOpacity={0.7}
    >
      <Text style={styles.letterText}>{item.letter}</Text>
      <Text style={styles.emojiText}>{item.emoji}</Text>
      <Text style={styles.wordText}>{item.word}</Text>
    </TouchableOpacity>
  );

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.headerTitle}>Learn ABC</Text>
        <Text style={styles.headerSubtitle}>Tap on any letter to learn!</Text>
      </View>

      <ScrollView 
        style={styles.scrollView}
        showsVerticalScrollIndicator={false}
      >
        <View style={styles.alphabetGrid}>
          {alphabetData.map((item, index) => renderAlphabetItem(item, index))}
        </View>
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F8F9FA',
  },
  header: {
    alignItems: 'center',
    paddingVertical: 20,
    paddingHorizontal: 20,
    backgroundColor: '#FF6B6B',
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#fff',
    marginBottom: 5,
  },
  headerSubtitle: {
    fontSize: 16,
    color: '#fff',
    opacity: 0.9,
  },
  scrollView: {
    flex: 1,
  },
  alphabetGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-around',
    padding: 15,
  },
  alphabetCard: {
    width: (width - 60) / 3,
    height: 120,
    backgroundColor: '#fff',
    borderRadius: 15,
    marginBottom: 15,
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 3,
    borderWidth: 2,
    borderColor: '#E0E0E0',
  },
  selectedCard: {
    borderColor: '#FF6B6B',
    backgroundColor: '#FFF5F5',
  },
  letterText: {
    fontSize: 32,
    fontWeight: 'bold',
    color: '#FF6B6B',
    marginBottom: 5,
  },
  emojiText: {
    fontSize: 24,
    marginBottom: 5,
  },
  wordText: {
    fontSize: 12,
    color: '#666',
    textAlign: 'center',
  },
});

export default AlphabetScreen;
