import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
} from 'react-native';

const EnglishScreen = ({ navigation }) => {
  const englishOptions = [
    {
      title: 'ABC Learning',
      subtitle: 'Learn A to Z',
      color: '#FF6B6B',
      screen: 'Alphabet',
      icon: '🔤',
    },
    {
      title: 'Nursery Rhymes',
      subtitle: 'Fun Songs & Poems',
      color: '#FFB74D',
      screen: 'Rhymes',
      icon: '🎵',
    },
  ];

  const renderOption = (item, index) => (
    <TouchableOpacity
      key={index}
      style={[styles.optionCard, { backgroundColor: item.color }]}
      onPress={() => navigation.navigate(item.screen)}
      activeOpacity={0.8}
    >
      <Text style={styles.optionIcon}>{item.icon}</Text>
      <Text style={styles.optionTitle}>{item.title}</Text>
      <Text style={styles.optionSubtitle}>{item.subtitle}</Text>
    </TouchableOpacity>
  );

  return (
    <ScrollView style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.headerTitle}>English Learning</Text>
        <Text style={styles.headerSubtitle}>Choose what you want to learn!</Text>
      </View>

      <View style={styles.optionsContainer}>
        {englishOptions.map((item, index) => renderOption(item, index))}
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F8F9FA',
  },
  header: {
    alignItems: 'center',
    paddingVertical: 30,
    paddingHorizontal: 20,
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 10,
  },
  headerSubtitle: {
    fontSize: 16,
    color: '#666',
  },
  optionsContainer: {
    paddingHorizontal: 20,
    paddingBottom: 20,
  },
  optionCard: {
    width: '100%',
    height: 120,
    borderRadius: 15,
    marginBottom: 15,
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  optionIcon: {
    fontSize: 40,
    marginBottom: 8,
  },
  optionTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#fff',
    marginBottom: 4,
  },
  optionSubtitle: {
    fontSize: 14,
    color: '#fff',
    opacity: 0.9,
  },
});

export default EnglishScreen;
