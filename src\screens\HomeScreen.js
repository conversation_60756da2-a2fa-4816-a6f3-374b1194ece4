import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  Dimensions,
} from 'react-native';

const { width } = Dimensions.get('window');

const HomeScreen = ({ navigation }) => {
  const menuItems = [
    {
      title: 'English',
      subtitle: 'ABCD & Rhymes',
      color: '#FF6B6B',
      screen: 'English',
      icon: '🔤',
    },
    {
      title: 'Math',
      subtitle: 'Numbers 1-100',
      color: '#4ECDC4',
      screen: 'Math',
      icon: '🔢',
    },
    {
      title: 'Hindi',
      subtitle: 'क ख ग घ...',
      color: '#45B7D1',
      screen: 'Hindi',
      icon: '🇮🇳',
    },
    {
      title: 'National Anthem',
      subtitle: 'Jana <PERSON>',
      color: '#96CEB4',
      screen: 'NationalAnthem',
      icon: '🎵',
    },
  ];

  const renderMenuItem = (item, index) => (
    <TouchableOpacity
      key={index}
      style={[styles.menuItem, { backgroundColor: item.color }]}
      onPress={() => navigation.navigate(item.screen)}
      activeOpacity={0.8}
    >
      <Text style={styles.menuIcon}>{item.icon}</Text>
      <Text style={styles.menuTitle}>{item.title}</Text>
      <Text style={styles.menuSubtitle}>{item.subtitle}</Text>
    </TouchableOpacity>
  );

  return (
    <ScrollView style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.welcomeText}>Welcome to</Text>
        <Text style={styles.appTitle}>Kids Tutorial App</Text>
        <Text style={styles.subtitle}>Learn & Have Fun! 🎉</Text>
      </View>

      <View style={styles.menuContainer}>
        {menuItems.map((item, index) => renderMenuItem(item, index))}
      </View>

      <View style={styles.footer}>
        <Text style={styles.footerText}>Made with ❤️ for Kids</Text>
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F8F9FA',
  },
  header: {
    alignItems: 'center',
    paddingVertical: 30,
    paddingHorizontal: 20,
  },
  welcomeText: {
    fontSize: 18,
    color: '#666',
    marginBottom: 5,
  },
  appTitle: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 10,
  },
  subtitle: {
    fontSize: 16,
    color: '#888',
  },
  menuContainer: {
    paddingHorizontal: 20,
    paddingBottom: 20,
  },
  menuItem: {
    width: '100%',
    height: 120,
    borderRadius: 15,
    marginBottom: 15,
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  menuIcon: {
    fontSize: 40,
    marginBottom: 8,
  },
  menuTitle: {
    fontSize: 22,
    fontWeight: 'bold',
    color: '#fff',
    marginBottom: 4,
  },
  menuSubtitle: {
    fontSize: 14,
    color: '#fff',
    opacity: 0.9,
  },
  footer: {
    alignItems: 'center',
    paddingVertical: 20,
  },
  footerText: {
    fontSize: 14,
    color: '#888',
  },
});

export default HomeScreen;
